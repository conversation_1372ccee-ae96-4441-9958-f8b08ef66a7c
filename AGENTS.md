# 通航微信小程序 - AI 开发助手指南

## 项目概述

这是一个基于 uni-app 框架开发的通用航空微信小程序，主要用于航空飞行任务管理、人员培训、安全保障等业务场景。

### 技术栈

- **框架**: uni-app (Vue 2)
- **UI组件**: Vant Weapp
- **状态管理**: Vuex
- **HTTP请求**: 自定义封装的 monkos 请求库
- **日期处理**: dayjs
- **开发工具**: ESLint + Prettier

### 项目结构

```
oc-wechat-ui/
├── api/                    # API接口定义
│   ├── index.js           # 请求配置和基础API
│   ├── flightTask.js      # 飞行任务相关API
│   └── weChat.js          # 微信相关API
├── assets/                # 静态资源
├── components/            # 公共组件
├── pages/                 # 页面文件
│   ├── home/             # 首页模块
│   ├── flightTask/       # 飞行任务模块
│   ├── flightPlan/       # 飞行计划模块
│   ├── login/            # 登录模块
│   ├── my/               # 个人中心模块
│   ├── study/            # 学习培训模块
│   ├── todo/             # 待办事项模块
│   └── scan/             # 扫码模块
├── utils/                # 工具函数
├── store/                # 状态管理
└── wxcomponents/         # 微信小程序组件
```

## 核心功能模块

### 1. 飞行任务管理 (flightTask)

- 飞行任务创建和编辑
- 任务列表查看
- 任务详情确认
- 人员分配和修改

### 2. 飞行计划 (flightPlan)

- 飞行计划查看
- 计划详情展示
- 机组人员更新
- 消息通知

### 3. 学习培训 (study)

- 飞行培训
- 乘客管理
- 订单处理
- 航班架次

### 4. 待办事项 (todo)

- 各类准备工作
- 审批流程
- 检查确认
- 评估记录

### 5. 个人中心 (my)

- 个人信息
- 飞行记录
- 培训记录
- 审批日期

## 开发规范和最佳实践

### 代码风格

- 使用中文注释和变量命名
- 遵循 Vue.js 最佳实践
- 使用 ESLint + Prettier 进行代码格式化
- 样式单位优先使用 px 而非 rpx

### 组件开发

- 保持组件的独立性和完整性
- 封装通用功能为公共组件
- 通过 props 传递配置参数
- 使用 emit 事件进行父子组件通信

### API 调用

- 统一使用封装的 monkos 请求库
- API 接口按模块分类管理
- 错误处理和状态管理统一处理

### 日期处理

- 统一使用 dayjs 库处理时间操作
- 封装日期工具函数提供相对时间描述
- 支持今天、明天、后天等相对时间显示

### 样式规范

- 使用 SCSS 预处理器
- 全局样式定义在 App.vue 中
- 组件样式使用 scoped 作用域

## 关键组件说明

### 1. CutomerNav - 自定义导航组件

- 支持滚动监听添加背景色和阴影
- 可配置返回按钮行为
- 支持自定义标题和操作按钮

### 2. CrewModify - 人员修改组件

- 支持单选和多选模式
- 通过类别参数区分不同页面差异
- 内置数据获取方法提高独立性

### 3. Empty - 空状态组件

- 使用 assets/images/empty.png 作为空状态图片
- 可配置提示文案
- 统一的空状态展示样式

### 4. 各类卡片组件

- task-card: 任务卡片
- study-*-card: 学习相关卡片
- fly-test-card: 飞行测试卡片

## API 接口规范

### 基础配置

- 生产环境: `https://ga.swcares.com.cn/trade/oc/wechat`
- 测试环境: `https://airportpe.natappvip.cc`
- 扫码模块: `https://ga.swcares.com.cn/sale/travel-wechat`

### 请求格式

- 统一使用 POST/GET/PUT 方法
- 请求参数分为 params、body、query 三种类型
- 响应数据统一处理和错误拦截

### 认证机制

- 基于 token 的身份认证
- 微信授权登录集成
- 自动处理登录状态和跳转

## 开发工作流

### 1. 环境准备

```bash
# 安装依赖
npm install

# 启动开发服务器
# 使用 HBuilderX 或其他 uni-app 开发工具
```

### 2. 新功能开发

1. 在对应模块下创建页面文件
2. 在 pages.json 中注册页面路由
3. 实现页面逻辑和样式
4. 添加 API 接口调用
5. 测试功能完整性

### 3. 组件开发

1. 在 components 目录下创建组件
2. 实现组件逻辑和样式
3. 编写组件文档和使用示例
4. 在页面中引入和使用组件

### 4. 代码提交

1. 运行 ESLint 检查代码质量
2. 使用 Prettier 格式化代码
3. 编写清晰的提交信息
4. 提交代码并推送到仓库

## 注意事项

### 兼容性

- 主要针对微信小程序平台开发
- 兼容不同版本的微信客户端
- 适配不同尺寸的移动设备

### 性能优化

- 图片资源压缩和懒加载
- 组件按需加载
- 合理使用缓存机制
- 避免内存泄漏

### 安全考虑

- 敏感信息加密传输
- 用户权限验证
- 防止 XSS 和 CSRF 攻击
- 数据校验和过滤

## 常见问题解决

### 1. 样式问题

- Vant 组件样式覆盖方法
- 响应式布局适配
- 字体和颜色规范

### 2. 数据处理

- 日期格式转换
- 数组和对象操作
- 异步数据加载

### 3. 组件通信

- 父子组件数据传递
- 兄弟组件通信
- 全局状态管理

### 4. 路由导航

- 页面跳转和参数传递
- 返回上一页处理
- 路由守卫和权限控制

## 联系方式

如有技术问题或需要支持，请联系开发团队或查阅相关文档。
