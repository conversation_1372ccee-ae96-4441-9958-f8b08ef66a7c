<template>
  <view>
    <view class="filter-box">
      <FormItem label="时间筛选">
        <view>
          <view class="btn-groups">
            <view
              class="custom-tag"
              v-for="item in dateOptionList"
              :class="
                filterForm.selectedDateType === item.value ? 'active' : ''
              "
              :key="item.value"
              @click="selectDate(item)"
            >
              {{ item.text }}
            </view>
          </view>
        </view>
      </FormItem>
      <FormItem
        label=" "
        clearable
        @clear="filterForm.flightDate = ''"
        v-if="filterForm.selectedDateType === '其他日期'"
      >
        <view class="input-box-line">
          <input
            v-model="filterForm.flightDate"
            placeholder="请选择"
            disabled
            @click="datePickerShow = true"
          />
        </view>
      </FormItem>
      <FormItem label="机型">
        <view class="btn-groups">
          <view
            class="custom-tag"
            v-for="item in aircraftTypeTextList"
            :class="filterForm.aircraftTypeText === item.value ? 'active' : ''"
            :key="item.value"
            @click="onAircraftTypeTextChange(item)"
          >
            {{ item.text }}
          </view>
        </view>
      </FormItem>
      <FormItem
        label=" "
        v-if="filterForm.aircraftTypeText === '其他'"
        clearable
        @clear="filterForm.aircraftType = ''"
      >
        <view class="input-box-line">
          <input
            v-model="filterForm.aircraftType"
            placeholder="请选择"
            disabled
            @click="openPicker('机型', aircraftTypeList, 'aircraftType')"
          />
        </view>
      </FormItem>

      <FormItem label="任务状态" v-if="pageType === 'taskList2'">
        <view class="btn-groups">
          <view
            class="custom-tag"
            v-for="item in taskStatusList"
            :class="filterForm.taskStatus === item.value ? 'active' : ''"
            :key="item.value"
            @click="onTaskStatusTextChange(item)"
          >
            {{ item.text }}
          </view>
        </view>
      </FormItem>

      <FormItem label="发布类型">
        <view class="btn-groups">
          <view
            class="custom-tag"
            v-for="item in publishTypeList"
            :class="filterForm.releaseType === item.value ? 'active' : ''"
            :key="item.value"
            @click="onPublishTypeTextChange(item)"
          >
            {{ item.text }}
          </view>
        </view>
      </FormItem>
      <view class="margin-top-12">
        <slot name="options"></slot>
      </view>
      <!-- 日期选择器 -->
      <van-calendar
        :show="datePickerShow"
        @close="datePickerShow = false"
        @confirm="onDateConfirm"
        color="#2C5DE5"
      />
      <!-- 下拉选择-->
      <van-popup :show="pickerData.show" position="bottom">
        <van-picker
          :columns="pickerData.list"
          @confirm="onPickerConfirm"
          @cancel="closePicker"
          show-toolbar
          :title="pickerData.title"
        />
      </van-popup>
    </view>
  </view>
</template>
<script>
import FormItem from '../../flightTask--copy/compoents/FormItem.vue'
import dayjs from 'dayjs'
import { DATE_FORMAT, SUCCESS_CODE } from '../../../utils/constant'
import { getAircraftStyle } from '../../../api/flightTask'

export default {
  name: 'FilterForm',
  components: { FormItem },
  props: {
    pageType: {
      type: String,
      default: 'taskList2', //taskList2:任务列表 taskList1:接受指派 flightPlan:航班计划,
    },
    filterForm: {
      type: Object,
      // default: () => ({
      //   selectedDateType: '',
      //   flightDate: '',
      //   aircraftType: '',
      //   aircraftTypeText: '',
      //   releaseType: 2,
      //   taskStatus: null,
      // }),
    },
  },
  data() {
    return {
      // filterForm: {
      //   selectedDateType: '',
      //   flightDate: '',
      //   aircraftType: '',
      //   aircraftTypeText: '',
      //   releaseType: 2,
      //   taskStatus: null,
      // },
      datePickerShow: false, //日期选择器
      aircraftTypeTextList: [
        { text: '所有', value: '' },
        { text: 'BELL429', value: 'BELL429' },
        {
          text: 'BELL505',
          value: 'BELL505',
        },
        { text: 'AW139', value: 'AW139' },
        { text: '其他', value: '其他' },
      ],
      aircraftTypeList: [], //机型列表
      taskStatusList: [
        { text: '所有', value: null },
        { text: '未确认', value: 0 },
        { text: '已确认', value: 1 },
      ], //发布类型列表
      publishTypeList: [
        { text: '我的', value: 1 },
        { text: '全部', value: 2 },
      ], //发布类型列表
      dateOptionList: [
        { text: '所有', value: '' },
        { text: '今天', value: '今天' },
        { text: '明天', value: '明天' },
        { text: '后天', value: '后天' },
        { text: '其他日期', value: '其他日期' },
      ],
      //下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
      },
    }
  },
  watch: {
    filterForm: {
      handler(newVal) {
        this.$emit('update:filterForm', newVal)
      },
      deep: true, // 深度监听对象变化
    },
  },
  onShow() {
    this.getAircraftType()
    // this.filterForm = this.defaultFilter || this.filterForm
  },
  methods: {
    // 选择日期
    selectDate(type) {
      this.filterForm.selectedDateType = type.value
      switch (type) {
        case '今天':
          this.selectedDate = dayjs().format(DATE_FORMAT)
          break
        case '明天':
          this.selectedDate = dayjs().add(1, 'day').format(DATE_FORMAT)
          break
        case '后天':
          this.selectedDate = dayjs().add(2, 'day').format(DATE_FORMAT)
          break
        case '其他日期':
          this.datePickerShow = true
          break
      }
    },
    //获取机型
    async getAircraftType() {
      const res = await getAircraftStyle()
      if (res.response.code === SUCCESS_CODE) {
        this.aircraftTypeList = res.response.data.map((item) => {
          return {
            text: item.aircraftTailNo || '',
            value: item.aircraftStyle || '',
          }
        })
      }
    },
    openPicker(title, list, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
      }
    },
    onPickerConfirm(ev) {
      this.filterForm[this.pickerData.formKey] = ev.detail.value.value
      this.closePicker()
    },
    //日期弹窗确定
    onDateConfirm(ev) {
      this.filterForm.flightDate = dayjs(ev.detail).format(DATE_FORMAT)
      this.datePickerShow = false
    },
    //机型点击
    onAircraftTypeTextChange(item) {
      this.filterForm.aircraftTypeText = item.value
      this.filterForm.aircraftType = item.value === '其他' ? '' : item.value
    },
    //发布类型点击
    onPublishTypeTextChange(item) {
      this.filterForm.releaseType = item.value
    },
    onTaskStatusTextChange(item) {
      this.filterForm.taskStatus = item.value
    },
  },
}
</script>

<style scoped lang="scss">
.filter-box {
  width: calc(100% - 32px);
  box-sizing: border-box;
  padding: 16px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin: 16px auto;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .btn-groups {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
  }

  .custom-tag {
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    border: 1px solid #2c5de5;
    color: #2c5de5;
    background: transparent;
    font-size: 12px;
    border-radius: 4px;

    &.active {
      background: #2c5de5;
      color: #fff;
    }
  }
}
</style>
