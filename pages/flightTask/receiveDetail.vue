<template>
  <view class="receive-detail-page">
    <!-- 自定义导航栏 -->
    <CustomerNav title="人员指派" />

    <!-- 内容主体 -->
    <view class="content">
      <!-- 表单区域 -->
      <view class="form-box">
        <van-row>
          <van-col span="12">
            <FormItem label="注册号：" class="text-row" label-width="60px">
              <text>{{ formData.registrationNumber }}</text>
            </FormItem>
          </van-col>
          <van-col span="12">
            <FormItem label="机型：" class="text-row" label-width="50px">
              <text>{{ formData.aircraftType }}</text>
            </FormItem>
          </van-col>
        </van-row>

        <FormItem label="任务性质：" class="text-row">
          <text>{{ formData.taskType }}</text>
        </FormItem>

        <FormItem label="计划时间：" class="text-row">
          <text
            >{{ formData.flightDate || '' }}({{
              getRelativeDateText(formData.flightDate)
            }})
          </text>
        </FormItem>

        <FormItem
          label="起飞基地："
          class="text-row"
          v-if="!!formData.departure"
        >
          <text>{{ formData.departure }}</text>
        </FormItem>
        <FormItem
          label="航线/空域："
          class="text-row"
          v-if="!!formData.routeOrAirspaceName"
        >
          <text>{{ formData.routeOrAirspaceName }}</text>
        </FormItem>
        <FormItem label="预估架次：" class="text-row">
          <text>{{ formData.flightFrequency }}</text>
        </FormItem>
        <view class="plan-time-content">
          <view
            v-for="(item, index) in formData.takeOffAndLanding"
            :key="index"
            class="plan-time-item"
          >
            <van-row>
              <van-col span="12">
                <FormItem
                  label="计划起飞："
                  class="text-row time-row"
                  label-width="70px"
                >
                  <text>{{ item.planDepartTime }}</text>
                </FormItem>
              </van-col>
              <van-col span="10">
                <FormItem
                  label="计划到达："
                  class="text-row time-row"
                  label-width="70px"
                >
                  <text>{{ item.planArriveTime || '' }}</text>
                </FormItem>
              </van-col>
            </van-row>
            <FormItem
              label="起飞基地："
              class="text-row time-row"
              label-width="70px"
            >
              <text>{{ item.departure || '' }}</text>
            </FormItem>
            <FormItem
              label="到达基地："
              class="text-row time-row"
              label-width="70px"
            >
              <text>{{ item.arrive || '' }}</text>
            </FormItem>
            <FormItem
              label="备注："
              class="text-row time-row"
              label-width="70px"
            >
              <text>{{ item.sortiesRemark || '' }}</text>
            </FormItem>
          </view>
        </view>
        <view>
          <view class="divider"></view>

          <!-- 使用公共人员修改组件 -->
          <CrewModify
            ref="crewModify"
            type="receive"
            :initial-data="formData"
            :multi-select-fields="multiSelectFields"
            @data-change="onCrewDataChange"
          />
        </view>
      </view>
      <!-- 提交按钮 -->
      <view
        class="flex-row justify-between gap-12"
        v-if="getDepartmentPermissions('1,2,3,4').showOption"
      >
        <view class="submit-btn-box normal-btn-box" @click="submitForm(2)">
          保存
        </view>
        <view class="submit-btn-box" @click="submitForm(1)"> 提交</view>
      </view>
    </view>

    <Background />
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import FormItem from './compoents/FormItem.vue'
import CrewModify from '../../components/CrewModify/index.vue'
import {
  planConfirmFlightTask2,
  queryFlightTaskConfigDetail,
} from '../../api/flightTask'
import { SUCCESS_CODE } from '../../utils/constant'
import { getDepartmentPermissions, getRelativeDateText } from '../../utils'
import Background from '../../components/Background/index.vue'

export default {
  name: 'sureDetail',
  components: { Background, CustomerNav, FormItem, CrewModify },
  data() {
    return {
      taskId: '',
      formData: {},
      formNameData: {}, //表单名称数据展示用
      formIdData: {}, //表单id数据
      // 配置哪些字段支持多选
      multiSelectFields: ['mechanicId'],
    }
  },
  onLoad: function (option) {
    this.taskId = option.id
  },

  mounted() {
    this.getData()
  },
  methods: {
    getDepartmentPermissions,
    getRelativeDateText,

    //获取详情
    async getData() {
      const res = await queryFlightTaskConfigDetail({
        flightTaskConfigId: Number(this.taskId),
      })
      if (res.response.code === 200) {
        const data = res.response.data
        this.formData = data || {}
      }
    },

    // 处理组件数据变化
    onCrewDataChange(data) {
      this.formIdData = { ...this.formIdData, ...data.formIdData }
      this.formNameData = { ...this.formNameData, ...data.formNameData }
    },

    // 提交表单
    async submitForm(type) {
      const params = {
        ...this.formIdData,
        flightTaskConfigId: Number(this.taskId),
        confirmStatus: type,
      }
      for (let key in params) {
        if (
          params[key] === undefined ||
          params[key] === '' ||
          params[key] === null
        ) {
          delete params[key]
        }
      }
      const { response } = await planConfirmFlightTask2(params)
      if (response.code === SUCCESS_CODE) {
        uni.showToast({
          title: response.msg || '操作成功',
          icon: 'success',
        })
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/flightTask/taskList?pageType=1',
          })
        }, 1500)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../assets/css/common.less';

.receive-detail-page {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

.content {
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 表单样式 */
.form-box {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  /deep/ .text-row {
    word-break: break-all; /* 强制在单词内换行 */
    white-space: pre-line;

    .value-box {
      border-bottom: none;
      padding: 0;
    }

    .label-box {
      padding: 0;
      font-size: 14px;
      color: #323233;
    }

    .input-box {
      border-bottom: 1px solid #ccc;
    }
  }

  .time-row {
    white-space: nowrap;
  }

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }
}

.divider {
  width: 100%;
  height: 1px;
  background: rgba(204, 204, 204, 0.5);
  margin: 12px 0;
}

.submit-btn-box {
  flex: 1;
  background: #2c5de5;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
  padding: 8px 16px;
  margin-top: 16px;
  text-align: center;

  &:active {
    opacity: 0.8;
  }
}

.normal-btn-box {
  background: #fff;
  border: 1px solid #2c5de5;
  color: #2c5de5;
}

.plan-time-content {
  padding: 8px 1em;
  border: 1px dashed #ccc;
  border-radius: 4px;

  .plan-time-item {
    border-bottom: 1px dashed #ccc;

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
