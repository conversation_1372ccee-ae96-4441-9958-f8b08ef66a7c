import dayjs from 'dayjs'

/**
 * 根据传入的日期展示相对时间描述
 * @param {string|Date} date - 要比较的日期
 * @returns {string} 返回 '今天'、'明天'、'后天' 或 '距离今天x天'
 */
export function getRelativeDateText(date) {
  if (!date) return ''
  const targetDate = dayjs(date).startOf('day')
  const today = dayjs().startOf('day')

  // 计算天数差
  const diffDays = targetDate.diff(today, 'day')
  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '明天'
  } else if (diffDays === 2) {
    return '后天'
  } else if (diffDays > 0) {
    return `距离今天${diffDays}天`
  } else {
    return `${Math.abs(diffDays)}天前`
  }
}

/**
 * 根据roleType字符串判断用户是否有某个部门的编辑权限
 * @param {string} roleType - 角色类型字符串，如 "1,2,3,4"
 * @param {Array} departments - 可选，用户的部门数据数组，不传则从本地存储获取
 * @returns {object} canSee - 是否有查看权限，canEdit - 是否有编辑权限
 */
export function getDepartmentPermissions(roleType, departments = null) {
  // 如果没有传入departments，从本地存储获取
  if (!departments) {
    departments = uni.getStorageSync('departments')
      ? JSON.parse(uni.getStorageSync('departments'))
      : []
  }
  let role = { canSee: false, canEdit: false, showOption: false }
  const arr = []
  departments.forEach((item) => {
    if (item.isAdmin === 1) {
      arr.push(item)
    }
  })
  role.showOption = arr.length > 0
  //
  departments.some((item) => {
    role.showOption = arr.length > 0
    if (roleType.includes(String(item.deptType))) {
      role.canSee = true
      role.canEdit = item.isAdmin === 1
      return true
    }
    return false
  })

  return role
}
